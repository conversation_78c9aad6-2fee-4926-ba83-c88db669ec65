"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { toast } from "@/components/ui/use-toast"
import { Co<PERSON>, Share2, Gift, Crown, Users } from "lucide-react"
import { useReferralDataRealtime } from "@/lib/domains/referral/referral.realtime.hooks"
import { useReferralSharing } from "@/lib/domains/referral/referral.hooks"
import { useGenerateAndCreateReferralCode } from "@/lib/domains/referral/referral.hooks"
import { useUser } from "@/lib/domains/auth/auth.hooks"

/**
 * Achievement popup component
 */
interface AchievementPopupProps {
  achievement: {
    id: string
    name: string
    description: string
    isCompleted: boolean
  }
  onClose: () => void
}

function AchievementPopup({ achievement, onClose }: AchievementPopupProps) {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose()
    }, 5000) // Auto-close after 5 seconds

    return () => clearTimeout(timer)
  }, [onClose])

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md animate-in zoom-in-95 duration-300">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-gradient-to-r from-teal-500 to-yellow-500 rounded-full flex items-center justify-center mb-4">
            <Gift className="h-8 w-8 text-white" />
          </div>
          <CardTitle className="text-xl">🎉 Achievement Unlocked!</CardTitle>
          <CardDescription className="text-lg font-medium">{achievement.name}</CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-muted-foreground">{achievement.description}</p>
          <Button onClick={onClose} className="w-full">
            Awesome!
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Referrals settings component
 */
export function ReferralsSettings() {
  const user = useUser()
  const { referralCode, loading, hasReferralCode, goals, totalReferrals } =
    useReferralDataRealtime()
  const { referralLink, copyReferralLink, copyReferralCode } = useReferralSharing(referralCode)
  const generateReferralCode = useGenerateAndCreateReferralCode()
  const [showAchievement, setShowAchievement] = useState<string | null>(null)
  const [seenAchievements, setSeenAchievements] = useState<Set<string>>(new Set())

  // Check for new achievements
  useEffect(() => {
    const newAchievements = goals.filter(
      (goal) => goal.isCompleted && !seenAchievements.has(goal.id)
    )

    if (newAchievements.length > 0) {
      // Show the first new achievement
      const achievement = newAchievements[0]
      setShowAchievement(achievement.id)

      // Mark as seen
      setSeenAchievements((prev) => new Set([...prev, achievement.id]))
    }
  }, [goals, seenAchievements])

  const handleCopyReferralCode = async () => {
    const success = await copyReferralCode()
    if (success) {
      toast({
        title: "Copied!",
        description: "Referral code copied to clipboard",
      })
    } else {
      toast({
        title: "Failed to copy",
        description: "Please try again",
        variant: "destructive",
      })
    }
  }

  const handleCopyReferralLink = async () => {
    const success = await copyReferralLink()
    if (success) {
      toast({
        title: "Copied!",
        description: "Referral link copied to clipboard",
      })
    } else {
      toast({
        title: "Failed to copy",
        description: "Please try again",
        variant: "destructive",
      })
    }
  }

  const handleShareReferralLink = async () => {
    if (navigator.share && referralLink) {
      try {
        await navigator.share({
          title: "Join me on Togeda.ai!",
          text: "Plan amazing trips with friends using my referral code",
          url: referralLink,
        })
      } catch (error) {
        // Fallback to copy
        handleCopyReferralLink()
      }
    } else {
      handleCopyReferralLink()
    }
  }

  const handleGenerateCode = async () => {
    if (!user?.uid) {
      toast({
        title: "Error",
        description: "User not authenticated",
        variant: "destructive",
      })
      return
    }

    try {
      const code = await generateReferralCode(user.uid)
      if (code) {
        toast({
          title: "Referral code generated!",
          description: `Your referral code is: ${code}`,
        })
      } else {
        toast({
          title: "Failed to generate code",
          description: "Please try again",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate referral code",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Referrals</CardTitle>
          <CardDescription>Loading your referral information...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-10 bg-muted rounded"></div>
            <div className="h-4 bg-muted rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Your Referral Code
          </CardTitle>
          <CardDescription>
            Share your referral code with friends and earn rewards when they join!
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {hasReferralCode && referralCode ? (
            <>
              <div className="space-y-2">
                <Label htmlFor="referralCode">Referral Code</Label>
                <div className="flex gap-2">
                  <Input
                    id="referralCode"
                    value={referralCode.id}
                    readOnly
                    className="font-mono text-lg"
                  />
                  <Button variant="outline" size="icon" onClick={handleCopyReferralCode}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="referralLink">Referral Link</Label>
                <div className="flex gap-2">
                  <Input
                    id="referralLink"
                    value={referralLink || ""}
                    readOnly
                    className="text-sm"
                  />
                  <Button variant="outline" size="icon" onClick={handleCopyReferralLink}>
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon" onClick={handleShareReferralLink}>
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Users className="h-4 w-4" />
                <span>{totalReferrals} successful referrals</span>
              </div>
            </>
          ) : (
            <div className="text-center space-y-4">
              <p className="text-muted-foreground">You don't have a referral code yet.</p>
              <Button onClick={handleGenerateCode}>Generate Referral Code</Button>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            Referral Goals
          </CardTitle>
          <CardDescription>Invite friends and unlock amazing rewards!</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {goals.map((goal) => (
            <div key={goal.id} className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {goal.perkType === "squad" ? (
                    <Users className="h-4 w-4 text-teal-600" />
                  ) : (
                    <Crown className="h-4 w-4 text-yellow-600" />
                  )}
                  <span className="font-medium">{goal.name}</span>
                  {goal.isCompleted && (
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      Unlocked
                    </Badge>
                  )}
                </div>
                <span className="text-sm text-muted-foreground">
                  {goal.progress.current}/{goal.progress.target} referrals
                </span>
              </div>

              <Progress value={goal.progress.percentage} className="h-2" />

              <p className="text-sm text-muted-foreground">{goal.description}</p>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Achievement popup */}
      {showAchievement && (
        <AchievementPopup
          achievement={goals.find((g) => g.id === showAchievement)!}
          onClose={() => setShowAchievement(null)}
        />
      )}
    </>
  )
}
