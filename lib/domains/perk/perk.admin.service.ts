import { getAdminInstance } from "@/lib/firebase-admin"

/**
 * Admin Perk Service for server-side operations
 * Uses Firebase Admin SDK for operations that don't require user authentication
 */
export class PerkAdminService {
  private static readonly GLOBAL_PERKS_COLLECTION = "perks"
  private static readonly USER_PERKS_SUBCOLLECTION = "perks"

  /**
   * Check and unlock eligible perks for a user based on their referral count
   */
  static async unlockEligiblePerks(userId: string, totalReferrals: number): Promise<string[]> {
    try {
      console.log("🎁 [Admin] Checking for eligible perks for user:", userId, "with", totalReferrals, "referrals")

      const { adminDb, adminFieldValue } = await getAdminInstance()
      if (!adminDb || !adminFieldValue) {
        throw new Error("Firebase Admin is not initialized")
      }

      // Get all global perks
      const globalPerksSnapshot = await adminDb.collection(this.GLOBAL_PERKS_COLLECTION).get()
      
      if (globalPerksSnapshot.empty) {
        console.log("ℹ️ [Admin] No global perks found")
        return []
      }

      const unlockedPerks: string[] = []

      // Check each global perk
      for (const perkDoc of globalPerksSnapshot.docs) {
        const perkData = perkDoc.data()
        const perkId = perkDoc.id
        
        console.log("🔍 [Admin] Checking perk:", perkId, "requires:", perkData.requiredReferrals, "referrals")

        // Check if user has enough referrals for this perk
        if (totalReferrals >= perkData.requiredReferrals) {
          // Check if user already has this perk
          const userPerkRef = adminDb
            .collection("users")
            .doc(userId)
            .collection(this.USER_PERKS_SUBCOLLECTION)
            .doc(perkId)

          const existingPerk = await userPerkRef.get()

          if (!existingPerk.exists) {
            // User doesn't have this perk yet, unlock it
            console.log("🎉 [Admin] Unlocking perk:", perkId, "for user:", userId)

            await userPerkRef.set({
              perkId,
              perkType: perkData.perkType,
              name: perkData.name,
              description: perkData.description,
              isActive: true,
              unlockedAt: adminFieldValue.serverTimestamp(),
              createdAt: adminFieldValue.serverTimestamp(),
              updatedAt: adminFieldValue.serverTimestamp(),
            })

            unlockedPerks.push(perkId)
          } else {
            console.log("ℹ️ [Admin] User already has perk:", perkId)
          }
        } else {
          console.log("❌ [Admin] User doesn't have enough referrals for perk:", perkId)
        }
      }

      console.log("✅ [Admin] Perk checking completed. Unlocked perks:", unlockedPerks)
      return unlockedPerks
    } catch (error) {
      console.error("💥 [Admin] Error checking for eligible perks:", error)
      return []
    }
  }
}
